
export interface Trial {
  id: string;
  name: string;
  sponsor: string;
  patients: Patient[];
  priceList: PriceItem[];
}

export interface Patient {
  id: string;
  name: string;
}

export interface PriceItem {
  id: string;
  articleNr: string;
  articleName: string;
  totalPrice: number;
  vendorSplit: VendorSplit[];
}

export interface VendorSplit {
  vendorName: string;
  price: number;
}

export interface Task {
  id: string;
  trialId: string;
  trialName: string;
  articleNr: string;
  articleName: string;
  patientReference: string;
  employeeName: string;
  date: string;
  confirmed: boolean;
  invoiced: boolean;
  planned: boolean;
  deleted: boolean;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  date: string;
  tasks: Task[];
  totalAmount: number;
  status: 'draft' | 'sent' | 'paid';
}

export interface Employee {
  id: string;
  name: string;
  roleId?: string;
}

export interface Role {
  id: string;
  name: string;
  permissions: {
    tasks: {
      create: boolean;
      view: boolean;
      edit: boolean;
      delete: boolean;
      confirm: boolean;
    };
    invoices: {
      view: boolean;
      create: boolean;
    };
    trials: {
      create: boolean;
      edit: boolean;
    };
    employees: {
      edit: boolean;
      create: boolean;
    };
    sponsors: {
      edit: boolean;
      create: boolean;
    };
  };
}

export type TaskFilters = {
  patientReference?: string;
  trialId?: string;
  articleNr?: string;
  dateFrom?: string;
  dateTo?: string;
  confirmed?: boolean;
  invoiced?: boolean;
  planned?: boolean;
};

export interface LoginFormData {
  email: string;
  password: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
}
