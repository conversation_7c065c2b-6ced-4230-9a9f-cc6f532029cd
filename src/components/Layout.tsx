
import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ClipboardList, FolderOpen, FileText, Receipt, Settings, LogOut, User } from 'lucide-react';
import { toast } from 'sonner';
import { getUserSession, logoutUser } from '@/lib/auth';
import { User as UserType } from '@/types';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout = ({ children }: LayoutProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [currentUser, setCurrentUser] = useState<UserType | null>(null);

  const isActive = (path: string) => location.pathname === path;

  useEffect(() => {
    const user = getUserSession();
    setCurrentUser(user);

    // Listen for login events to update user session
    const handleStorageChange = () => {
      const updatedUser = getUserSession();
      setCurrentUser(updatedUser);
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('user-login', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('user-login', handleStorageChange);
    };
  }, []);

  const handleLogout = () => {
    logoutUser();
    setCurrentUser(null);
    toast.success('Logged out successfully', {
      description: 'You have been signed out of your account.',
    });
    navigate('/login');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <Link to="/" className="flex items-center">
              <ClipboardList className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-bold text-gray-900">Trial Budged Tracker</h1>
            </Link>
            <div className="flex items-center space-x-4">
              <Link to="/">
                <Button
                  variant={isActive('/') ? 'default' : 'ghost'}
                  className="flex items-center space-x-2"
                >
                  <ClipboardList className="h-4 w-4" />
                  <span>Report Task</span>
                </Button>
              </Link>
              <Link to="/trials">
                <Button
                  variant={isActive('/trials') ? 'default' : 'ghost'}
                  className="flex items-center space-x-2"
                >
                  <FolderOpen className="h-4 w-4" />
                  <span>Trials</span>
                </Button>
              </Link>
              <Link to="/tasks">
                <Button
                  variant={isActive('/tasks') ? 'default' : 'ghost'}
                  className="flex items-center space-x-2"
                >
                  <FileText className="h-4 w-4" />
                  <span>Tasks</span>
                </Button>
              </Link>
              <Link to="/invoices">
                <Button
                  variant={isActive('/invoices') ? 'default' : 'ghost'}
                  className="flex items-center space-x-2"
                >
                  <Receipt className="h-4 w-4" />
                  <span>Invoices</span>
                </Button>
              </Link>
              <Link to="/settings">
                <Button
                  variant={isActive('/settings') ? 'default' : 'ghost'}
                  className="flex items-center space-x-2"
                >
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                </Button>
              </Link>

              {/* User Info and Logout */}
              {currentUser && (
                <>
                  <div className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-md">
                    <User className="h-4 w-4 text-gray-600" />
                    <span className="text-sm text-gray-700">{currentUser.name}</span>
                  </div>
                  <Button
                    variant="ghost"
                    onClick={handleLogout}
                    className="flex items-center space-x-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <LogOut className="h-4 w-4" />
                    <span>Logout</span>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {children}
      </main>
    </div>
  );
};

export default Layout;
